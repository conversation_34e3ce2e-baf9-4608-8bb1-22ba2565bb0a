import type { Ticket } from './types'

export const createCardObject = (): Ticket => {
    return {
        id: '',
        name: '',
        parentId: null,
        label: '',
        createdAt: new Date().toISOString(),
        assignedBy: '',
    }
}

export const taskLabelColors: Record<string, string> = {
    'Live issue': 'bg-rose-200 dark:bg-rose-200 dark:text-gray-900',
    Task: 'bg-blue-200 dark:bg-blue-200 dark:text-gray-900',
    Bug: 'bg-amber-200 dark:bg-amber-200 dark:text-gray-900',
    'Low priority': 'bg-purple-200 dark:bg-purple-200 dark:text-gray-900',
}

export const labelList = ['Task', 'Bug', 'Live issue', 'Low priority']
