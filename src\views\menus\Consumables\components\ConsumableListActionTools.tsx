import { useState } from 'react'
import Button from '@/components/ui/Button'
import { TbCloudDownload, TbPlus } from 'react-icons/tb'
import useConsumableList from '../hooks/useConsumableList'
import { CSVLink } from 'react-csv'
import ConsumableModal from './ConsumableModal'
import useTranslation from '@/utils/hooks/useTranslation'

const ConsumableListActionTools = () => {
    const [isModalOpen, setIsModalOpen] = useState(false)
    const { consumableList, mutate } = useConsumableList()
    const { t } = useTranslation()

    const handleAddConsumable = () => {
        setIsModalOpen(true)
    }

    const handleModalClose = () => {
        setIsModalOpen(false)
    }

    const handleSuccess = () => {
        // Refresh the list after successful creation/update
        mutate()
    }

    return (
        <>
            <div className="flex flex-col md:flex-row gap-3">
                <CSVLink filename="product-list.csv" data={consumableList}>
                    <Button icon={<TbCloudDownload className="text-xl" />}>
                        {t('nav.shared.export')}
                    </Button>
                </CSVLink>
                <Button
                    variant="solid"
                    icon={<TbPlus className="text-xl" />}
                    onClick={handleAddConsumable}
                >
                    {t('nav.shared.add')}
                </Button>
            </div>

            <ConsumableModal
                isOpen={isModalOpen}
                onClose={handleModalClose}
                onSuccess={handleSuccess}
            />
        </>
    )
}

export default ConsumableListActionTools
