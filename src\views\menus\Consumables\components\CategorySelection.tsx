import Card from '@/components/ui/Card'
import { FormItem } from '@/components/ui/Form'
import { Controller } from 'react-hook-form'
import classNames from '@/utils/classNames'
import { TbBox, TbShoppingBag, TbLink, TbPlus } from 'react-icons/tb'
import type { FormSectionBaseProps } from '../types'

type CategorySelectionProps = FormSectionBaseProps

type CategoryOption = {
    value: string
    label: string
    icon: React.ReactNode
}

const categoryOptions: CategoryOption[] = [
    {
        value: 'صناديق',
        label: 'صناديق',
        icon: <TbBox className="text-3xl" />,
    },
    {
        value: 'أكياس',
        label: 'أكياس',
        icon: <TbShoppingBag className="text-3xl" />,
    },
    {
        value: 'أفيز',
        label: 'أفيز',
        icon: <TbLink className="text-3xl" />,
    },
    {
        value: 'others',
        label: 'Add',
        icon: <TbPlus className="text-3xl" />,
    },
]

const CategorySelection = ({ control, errors }: CategorySelectionProps) => {
    return (
        <Card className="p-0 w-full">
            <FormItem
                className="m-0 p-0"
                label="Category"
                invalid={Boolean(errors.category)}
                errorMessage={errors.category?.message}
            >
                <Controller
                    name="category"
                    control={control}
                    render={({ field }) => (
                        <div className="grid grid-cols-2 md:grid-cols-3  gap-4">
                            {categoryOptions.map((option) => (
                                <div
                                    key={option.value}
                                    className={classNames(
                                        'border-2 rounded-lg p-2 cursor-pointer transition-all duration-200 hover:shadow-md ',
                                        field.value === option.value
                                            ? 'border-primary bg-primary/5 shadow-md'
                                            : 'border-gray-200 dark:border-gray-600 hover:border-primary/50',
                                    )}
                                    onClick={() => field.onChange(option.value)}
                                >
                                    <div className="flex flex-col items-center text-center space-y-3">
                                        <div
                                            className={classNames(
                                                'p-4 rounded-full transition-all duration-200',
                                                field.value === option.value
                                                    ? 'text-primary bg-primary/10 scale-110'
                                                    : 'text-gray-500 bg-gray-100 dark:bg-gray-700 hover:scale-105',
                                            )}
                                        >
                                            {option.icon}
                                        </div>
                                        <div>
                                            <h5
                                                className={classNames(
                                                    'font-semibold text-lg mb-1 transition-colors',
                                                    field.value === option.value
                                                        ? 'text-primary'
                                                        : 'text-gray-700 dark:text-gray-300',
                                                )}
                                            >
                                                {option.label}
                                            </h5>
                                        </div>
                                        {field.value === option.value && (
                                            <div className="w-2 h-2 bg-primary rounded-full"></div>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                />
            </FormItem>
        </Card>
    )
}

export default CategorySelection
