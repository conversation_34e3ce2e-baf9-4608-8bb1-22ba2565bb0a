import ApiService from './ApiService'

export async function apiGetConsumableList<
    T,
    U extends Record<string, unknown>,
>(params: U) {
    return ApiService.fetchDataWithAxios<T>({
        url: '/consumables',
        method: 'get',
        params,
    })
}

export async function apiGetConsumable<T, U extends Record<string, unknown>>({
    id,
    ...params
}: U) {
    return ApiService.fetchDataWithAxios<T>({
        url: `/consumables/${id}`,
        method: 'get',
        params,
    })
}
