import classNames from '@/utils/classNames'
import useLayout from '@/utils/hooks/useLayout'
import type { CommonProps } from '@/@types/common'
import { LAYOUT_CONTENT_OVERLAY } from '@/constants/theme.constant'

export type BottomStickyBarProps = CommonProps

const BottomStickyBar = ({ children }: BottomStickyBarProps) => {
    const { type } = useLayout()

    return (
        <div
            className={classNames(
                'bottom-0 right-0',
                type === LAYOUT_CONTENT_OVERLAY ? 'fixed' : 'sticky',
            )}
        >
            {children}
        </div>
    )
}

export default BottomStickyBar
