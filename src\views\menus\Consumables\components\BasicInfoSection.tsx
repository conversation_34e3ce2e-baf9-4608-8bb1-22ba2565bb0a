import Card from '@/components/ui/Card'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import { FormItem } from '@/components/ui/Form'
import NumericInput from '@/components/shared/NumericInput'
import { Controller } from 'react-hook-form'
import type { FormSectionBaseProps } from '../types'

type BasicInfoSectionProps = FormSectionBaseProps

const statusOptions = [
    { value: 'معتمد', label: 'معتمد' },
    { value: 'غير معتمد', label: 'غير معتمد' },
]

const warehouseOptions = [
    { value: 'المخزن الرئيسي', label: 'المخزن الرئيسي' },
    { value: 'فرع القاهرة', label: 'فرع القاهرة' },
    { value: 'فرع الإسكندرية', label: 'فرع الإسكندرية' },
    { value: 'فرع طنطا', label: 'فرع طنطا' },
    { value: 'فرع المنصورة', label: 'فرع المنصورة' },
]

const BasicInfoSection = ({ control, errors }: BasicInfoSectionProps) => {
    return (
        <Card className="w-full p-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-1">
                <FormItem
                    label="Description"
                    invalid={Boolean(errors.description)}
                    errorMessage={errors.description?.message}
                >
                    <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder="Enter description..."
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    label="Count"
                    invalid={Boolean(errors.count)}
                    errorMessage={errors.count?.message}
                >
                    <Controller
                        name="count"
                        control={control}
                        render={({ field }) => (
                            <NumericInput
                                type="text"
                                autoComplete="off"
                                placeholder="0"
                                value={field.value}
                                onChange={field.onChange}
                            />
                        )}
                    />
                </FormItem>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItem
                    label="Warehouse"
                    invalid={Boolean(errors.wareHouse)}
                    errorMessage={errors.wareHouse?.message}
                >
                    <Controller
                        name="wareHouse"
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={warehouseOptions}
                                placeholder="Select warehouse..."
                                value={warehouseOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    field.onChange(option?.value || '')
                                }
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    label="Status"
                    invalid={Boolean(errors.status)}
                    errorMessage={errors.status?.message}
                >
                    <Controller
                        name="status"
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={statusOptions}
                                placeholder="Select status..."
                                value={statusOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    field.onChange(option?.value || '')
                                }
                            />
                        )}
                    />
                </FormItem>
            </div>

            <FormItem
                label="Attachments"
                invalid={Boolean(errors.attachments)}
                errorMessage={errors.attachments?.message}
            >
                <Controller
                    name="attachments"
                    control={control}
                    render={({ field }) => (
                        <div className="space-y-2">
                            <Input
                                type="file"
                                accept="image/*,.pdf,.doc,.docx"
                                onChange={(e) => {
                                    const file = e.target.files?.[0]
                                    if (file) {
                                        field.onChange(file.name)
                                    }
                                }}
                            />
                            {field.value && (
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                    Selected: {field.value}
                                </div>
                            )}
                        </div>
                    )}
                />
            </FormItem>
        </Card>
    )
}

export default BasicInfoSection
