export type Ticket = {
    id: string
    name: string
    parentId: string | null
    createdAt: string
    assignedBy: string
    label: string
}

export type TreeNode = Ticket & { children?: TreeNode[] }

export function buildTreeFromFlat(tickets: Ticket[]): TreeNode[] {
    const map = new Map<string, TreeNode>()
    const roots: TreeNode[] = []

    tickets.forEach((ticket) => {
        map.set(ticket.id, { ...ticket, children: [] })
    })

    map.forEach((node) => {
        if (node.parentId && map.has(node.parentId)) {
            map.get(node.parentId)!.children!.push(node)
        } else {
            roots.push(node)
        }
    })

    return roots
}
