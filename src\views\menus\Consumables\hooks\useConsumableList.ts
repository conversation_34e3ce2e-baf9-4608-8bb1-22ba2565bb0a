import { apiGetConsumableList } from '@/services/ConsumableService'
import useSWR from 'swr'
import { useConsumableListStore } from '../store/consumableListStore'
import type { GetConsumableListResponse } from '../types'
import type { TableQueries } from '@/@types/common'

const useConsumableList = () => {
    const {
        tableData,
        filterData,
        setTableData,
        setFilterData,
        selectedConsumable,
        setSelectedConsumable,
        setSelectAllConsumable,
    } = useConsumableListStore((state) => state)

    const { data, error, isLoading, mutate } = useSWR(
        ['/api/consumables', { ...tableData, ...filterData }],
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        ([_, params]) =>
            apiGetConsumableList<GetConsumableListResponse, TableQueries>(
                params,
            ),
        {
            revalidateOnFocus: false,
        },
    )

    const consumableList = data?.list || []

    const consumableListTotal = data?.total || 0

    return {
        error,
        isLoading,
        tableData,
        filterData,
        mutate,
        consumableList,
        consumableListTotal,
        setTableData,
        selectedConsumable,
        setSelectedConsumable,
        setSelectAllConsumable,
        setFilterData,
    }
}

export default useConsumableList
