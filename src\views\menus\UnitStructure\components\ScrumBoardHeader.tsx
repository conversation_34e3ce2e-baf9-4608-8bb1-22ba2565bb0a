import Button from '@/components/ui/Button'
import { useScrumBoardStore } from '../store/scrumBoardStore'
import { TbBinaryTree2, TbListTree, TbPlus } from 'react-icons/tb'

const ScrumBoardHeader = () => {
    const { updateDialogView, openDialog } = useScrumBoardStore()

    const handleAddNewColumn = () => {
        updateDialogView('NEW_COLUMN')
        openDialog()
    }

    return (
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
            <div>
                <h3>Structure</h3>
                <p className="font-semibold">selected Project</p>
            </div>
            <div className="flex flex-col lg:flex-row justify-between lg:items-center gap-4">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                    <div className="flex items-center gap-2">
                        <Button
                            size="sm"
                            icon={<TbBinaryTree2 />}
                            onClick={() => 'for tree 1'}
                        />
                        <Button
                            size="sm"
                            icon={<TbListTree />}
                            onClick={() => 'for tree 2'}
                        />
                        <Button
                            size="sm"
                            icon={<TbPlus />}
                            onClick={handleAddNewColumn}
                        >
                            <span>New Board</span>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ScrumBoardHeader
