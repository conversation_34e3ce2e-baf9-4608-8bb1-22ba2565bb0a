import { create } from 'zustand'
import type { Ticket } from '../types'

type View = 'NEW_COLUMN' | 'TICKET' | 'ADD_MEMBER' | 'NEW_TICKET' | ''

export type ScrumBoardState = {
    items: Ticket[]
    ordered: string[]
    dialogOpen: boolean
    dialogView: View
    ticketId: string
    board: string
}

type ScrumBoardAction = {
    updateOrdered: (payload: string[]) => void
    updateItems: (payload: Ticket[]) => void
    openDialog: () => void
    closeDialog: () => void
    resetView: () => void
    updateDialogView: (payload: View) => void
    setSelectedTicketId: (payload: string) => void
    setSelectedBoard: (payload: string) => void
}

const initialState: ScrumBoardState = {
    items: [],
    ordered: [],
    dialogOpen: false,
    dialogView: '',
    ticketId: '',
    board: '',
}

export const useScrumBoardStore = create<ScrumBoardState & ScrumBoardAction>(
    (set) => ({
        ...initialState,
        updateOrdered: (payload) =>
            set(() => {
                return { ordered: payload }
            }),
        updateItems: (payload) => set(() => ({ items: payload })),
        openDialog: () => set({ dialogOpen: true }),
        closeDialog: () =>
            set({
                dialogOpen: false,
            }),
        resetView: () =>
            set({
                ticketId: '',
                board: '',
                dialogView: '',
            }),
        updateDialogView: (payload) => set(() => ({ dialogView: payload })),
        setSelectedTicketId: (payload) => set(() => ({ ticketId: payload })),
        setSelectedBoard: (payload) => set(() => ({ board: payload })),
    }),
)
