@layer components {
  .input-group {
    @apply flex items-center;
  
    .input {
      &:focus,
      &:focus-within {
        @apply z-10;
      }
    }
  
    .date-picker {
      z-index: 11;
    }
  
    .select-control-focused,
    .date-picker.picker-expanded,
    .input-suffix-end,
    .input-suffix-start {
      @apply z-20;
    }
  
    > .input,
    > .input-addon,
    > .button {
      &:first-child {
        @apply ltr:rounded-r-none! rtl:rounded-l-none!;
      }
  
      &:last-child:not(.picker) {
        @apply ltr:rounded-l-none! rtl:rounded-r-none!;
      }
  
      &:not(:first-child) {
        @apply ltr:rounded-l-none! ltr:-ml-px rtl:rounded-r-none! rtl:-mr-px;
      }
  
      &:not(:last-child) {
        @apply ltr:rounded-r-none! rtl:rounded-l-none!;
      }
    }
  
    > * {
      &:first-child {
        .input,
        .select__control {
          @apply ltr:rounded-r-none rtl:rounded-l-none;
        }
      }
  
      &:last-child {
        .input,
        .select__control {
          @apply ltr:rounded-l-none rtl:rounded-r-none;
        }
      }
  
      &:not(:first-child) {
        .input,
        .select__control {
          @apply ltr:rounded-l-none ltr:-ml-px rtl:rounded-r-none rtl:-mr-px;
        }
      }
  
      &:not(:last-child) {
        .input,
        .select__control {
          @apply ltr:rounded-r-none rtl:rounded-l-none;
        }
      }
    }
  
    >.button {
      @apply z-10 hover:z-20;
    }
  }  
}