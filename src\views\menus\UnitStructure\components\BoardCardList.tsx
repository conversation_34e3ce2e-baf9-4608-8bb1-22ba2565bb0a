import BoardCard from './BoardCard'
import { buildTreeFromFlat, TreeNode } from '../types'
import { Draggable, Droppable, DraggableChildrenFn } from '@hello-pangea/dnd'
import type { Ticket } from '../types'
import type { CSSProperties } from 'react'

export interface BaseBoardProps {
    contents?: Ticket[]
    useClone?: DraggableChildrenFn
    isCombineEnabled?: boolean
}

interface BoardCardListProps extends BaseBoardProps {
    ignoreContainerClipping?: boolean
    internalScroll?: boolean
    scrollContainerStyle?: CSSProperties
    isDropDisabled?: boolean
    listId?: string
    style?: CSSProperties
    listType?: string
    className?: string
}

const renderTree = (nodes: TreeNode[], level = 0) => {
    return nodes.map((node, index) => (
        <Draggable draggableId={node.id} index={index} key={node.id}>
            {(provided) => (
                <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    style={{
                        marginLeft: `${level * 16}px`, // تحكم في عمق التداخل
                        marginBottom: '8px',
                    }}
                >
                    <BoardCard data={node} />
                    {node.children && renderTree(node.children, level + 1)}
                </div>
            )}
        </Draggable>
    ))
}

const BoardCardList = (props: BoardCardListProps) => {
    const {
        ignoreContainerClipping,
        internalScroll,
        scrollContainerStyle,
        isDropDisabled,
        isCombineEnabled,
        listId = 'LIST',
        style,
        listType,
        contents,
        useClone,
    } = props

    return (
        <Droppable
            droppableId={listId}
            type={listType}
            ignoreContainerClipping={ignoreContainerClipping}
            isDropDisabled={isDropDisabled}
            isCombineEnabled={isCombineEnabled}
            renderClone={useClone}
        >
            {(dropProvided) => (
                <div
                    style={style}
                    className="board-wrapper overflow-hidden flex-auto"
                    {...dropProvided.droppableProps}
                >
                    {internalScroll ? (
                        <div
                            className="board-scrollContainer"
                            style={scrollContainerStyle}
                        >
                            <div
                                ref={dropProvided.innerRef}
                                className="board-dropzone h-full px-5"
                            >
                                {renderTree(buildTreeFromFlat(contents || []))}
                            </div>
                        </div>
                    ) : (
                        <div
                            ref={dropProvided.innerRef}
                            className="board-dropzone h-full px-5"
                        >
                            {renderTree(buildTreeFromFlat(contents || []))}
                        </div>
                    )}

                    {dropProvided.placeholder}
                </div>
            )}
        </Droppable>
    )
}

export default BoardCardList
