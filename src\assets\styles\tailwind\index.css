@import 'tailwindcss';

@config '../../../../tailwind.config.cjs';

@layer theme {
	:root {
		--neutral: #ffffff;
		--primary: #2a85ff;
		--primary-deep: #0069f6;
		--primary-mild: #4996ff;
		--primary-subtle: #2a85ff1a;
		--error: #ff6a55;
		--error-subtle: #ff6a551a;
		--success: #10b981;
		--success-subtle: #05eb7624;
		--info: #2a85ff;
		--info-subtle: #2a85ff1a;
		--warning: #f59e0b;
		--warning-subtle: #ffd40045;
		--gray-50: #fafafa;
		--gray-100: #f5f5f5;
		--gray-200: #e5e5e5;
		--gray-300: #d4d4d4;
		--gray-400: #a3a3a3;
		--gray-500: #737373;
		--gray-600: #525252;
		--gray-700: #404040;
		--gray-800: #262626;
		--gray-900: #171717;
		--gray-950: #0a0a0a;
	}

	.dark {
		--neutral: #ffffff;
		--primary: #2a85ff;
		--primary-deep: #0069f6;
		--primary-mild: #4996ff;
		--primary-subtle: #2a85ff1a;
		--error: #ff6a55;
		--error-subtle: #ff6a551a; 
		--success: #10b981;
		--success-subtle: #05eb7624;
		--info: #2a85ff;
		--info-subtle: #2a85ff1a;
		--warning: #f59e0b;
		--warning-subtle: #ffd40045;
		--gray-50: #fafafa;
		--gray-100: #f5f5f5;
		--gray-200: #e5e5e5;
		--gray-300: #d4d4d4;
		--gray-400: #a3a3a3;
		--gray-500: #737373;
		--gray-600: #525252;
		--gray-700: #404040;
		--gray-800: #262626;
		--gray-900: #171717;
		--gray-950: #0a0a0a;
	}
}

@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}

	button, [role=button] {
		cursor: pointer;
	}

	body {
		@apply text-gray-500 dark:text-gray-400 text-sm bg-gray-100 dark:bg-gray-950 leading-normal font-medium;
		-webkit-font-smoothing: antialiased;
	}

	h1,
	.h1 {
		@apply text-4xl font-bold text-gray-900 dark:text-gray-100;
	}

	h2,
	.h2 {
		@apply text-3xl font-bold text-gray-900 dark:text-gray-100;
	}

	h3,
	.h3 {
		@apply text-2xl font-bold text-gray-900 dark:text-gray-100;
	}

	h4,
	.h4 {
		@apply text-xl font-bold text-gray-900 dark:text-gray-100;
	}

	h5,
	.h5 {
		@apply text-lg font-bold text-gray-900 dark:text-gray-100;
	}

	h6,
	.h6 {
		@apply text-base font-semibold text-gray-900 dark:text-gray-100;
	}

	hr {
		@apply border-gray-200 dark:border-gray-600;
	}
}

@layer components {
	.heading-text {
		@apply text-gray-900 dark:text-gray-100;
	}
}